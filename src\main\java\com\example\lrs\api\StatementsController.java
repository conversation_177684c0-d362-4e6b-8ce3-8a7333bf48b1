
package com.example.lrs.api;

import com.example.lrs.domain.Statement;
import com.example.lrs.service.StatementService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.*;

@RestController
@RequestMapping("/xapi/statements")
@RequiredArgsConstructor
public class StatementsController {

    private final StatementService svc;

    @PostMapping(consumes = "application/json", produces = "application/json")
    public ResponseEntity<Object> postStatement(@RequestBody String body) throws Exception {
        String id = svc.saveOne(null, body);
        return ResponseEntity.ok(Collections.singletonList(id));
    }

    @PutMapping(params = "statementId", consumes = "application/json", produces = "application/json")
    public ResponseEntity<Object> putStatement(@RequestParam String statementId, @RequestBody String body) throws Exception {
        String id = svc.saveOne(statementId, body);
        return ResponseEntity.ok(Collections.singletonList(id));
    }

    @GetMapping(produces = "application/json")
    public Map<String,Object> getStatements(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime since,
            @RequestParam(required = false) String verb,
            @RequestParam(required = false, name = "activity") String activityId,
            @RequestParam(required = false) String registration,
            @RequestParam(required = false, defaultValue = "100") int limit
    ) {
        List<Statement> stmts = svc.query(since, verb, activityId, registration, limit);
        Map<String,Object> result = new LinkedHashMap<>();
        result.put("statements", stmts.stream().map(Statement::getFullStatement).toList());
        return result;
    }

    @GetMapping(params = "statementId", produces = "application/json")
    public ResponseEntity<String> getStatementById(@RequestParam String statementId) {
        String json = svc.getById(statementId);
        return json != null ? ResponseEntity.ok(json) : ResponseEntity.notFound().build();
    }
}
