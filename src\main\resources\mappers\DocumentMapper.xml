
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.lrs.mapper.DocumentMapper">

  <insert id="upsert" parameterType="map">
    INSERT INTO lrs_document
    (doc_type, activity_id, agent, agent_sha, registration, state_id, profile_id,
     content_type, content, etag)
    VALUES (#{docType}, #{activityId}, CAST(#{agentJson} AS JSON), #{agentSha},
            #{registration}, #{stateId}, #{profileId},
            #{contentType}, #{content,jdbcType=BLOB}, #{etag})
    ON DUPLICATE KEY UPDATE
      content_type = VALUES(content_type),
      content = VALUES(content),
      etag = VALUES(etag)
  </insert>

  <select id="findOne" parameterType="map" resultType="map">
    SELECT id, doc_type, activity_id, agent, agent_sha, registration, state_id, profile_id,
           content_type, content, etag, updated
    FROM lrs_document
    WHERE doc_type = #{docType}
      <if test="activityId != null">AND activity_id = #{activityId}</if>
      <if test="agentSha != null">AND agent_sha = #{agentSha}</if>
      <if test="registration != null">AND registration = #{registration}</if>
      <if test="stateId != null">AND state_id = #{stateId}</if>
      <if test="profileId != null">AND profile_id = #{profileId}</if>
    LIMIT 1
  </select>

  <delete id="delete" parameterType="map">
    DELETE FROM lrs_document
    WHERE doc_type = #{docType}
      <if test="activityId != null">AND activity_id = #{activityId}</if>
      <if test="agentSha != null">AND agent_sha = #{agentSha}</if>
      <if test="registration != null">AND registration = #{registration}</if>
      <if test="stateId != null">AND state_id = #{stateId}</if>
      <if test="profileId != null">AND profile_id = #{profileId}</if>
    LIMIT 1
  </delete>

</mapper>
