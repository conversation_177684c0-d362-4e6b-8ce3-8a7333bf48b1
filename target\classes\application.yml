server:
  port: 8080

spring:
  datasource:
    # MySQL 설정 (실제 운영 시 사용)
     url: ********************************************************************************************************
     username: nidsuser
     password: "@intermorph1!!"
     driver-class-name: com.mysql.cj.jdbc.Driver

    # H2 인메모리 데이터베이스 설정 (테스트용)
   # url: jdbc:h2:mem:lrs;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
   # username: sa
   # password:
   # driver-class-name: org.h2.Driver
  jackson:
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false

mybatis:
   mapper-locations: classpath:/mappers/*.xml
   configuration:
     map-underscore-to-camel-case: true
     default-statement-timeout: 5

lrs:
  auth:
    basic:
      username: admin
      password: admin
