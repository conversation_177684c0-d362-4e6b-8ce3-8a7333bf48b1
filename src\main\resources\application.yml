
server:
  port: 8080

spring:
  datasource:
    url: ********************************************************************************************
    username: lrs_user
    password: lrs_pass
  jackson:
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false

mybatis:
  mapper-locations: classpath:/mappers/*.xml
  configuration:
    map-underscore-to-camel-case: true
    default-statement-timeout: 5

lrs:
  auth:
    basic:
      username: admin
      password: admin
