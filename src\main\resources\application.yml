
server:
  port: 8080

spring:
  datasource:
    url: ********************************************************************************************************
    username: nidsuser
    password: "@intermorph1!!"
    driver-class-name: com.mysql.cj.jdbc.Driver
  jackson:
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false

mybatis:
  mapper-locations: classpath:/mappers/*.xml
  configuration:
    map-underscore-to-camel-case: true
    default-statement-timeout: 5

lrs:
  auth:
    basic:
      username: admin
      password: admin
