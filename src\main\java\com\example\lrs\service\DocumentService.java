
package com.example.lrs.service;

import com.example.lrs.mapper.DocumentMapper;
import com.example.lrs.util.HashUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service @RequiredArgsConstructor
public class DocumentService {
    private final DocumentMapper mapper;

    public String upsertState(String activityId, String agentJson, String registration, String stateId,
                              String contentType, byte[] content, String ifMatch, String ifNoneMatch) {
        String agentSha = agentJson != null ? HashUtil.sha256Hex(agentJson) : null;
        String newEtag = HashUtil.sha256Hex(content);

        Map<String,Object> key = new HashMap<>();
        key.put("docType", "state");
        key.put("activityId", activityId);
        key.put("agentSha", agentSha);
        key.put("registration", registration);
        key.put("stateId", stateId);

        Map<String,Object> existing = mapper.findOne(key);
        if (existing != null) {
            String currentEtag = (String) existing.get("etag");
            if (ifMatch != null && !stripQuotes(ifMatch).equals(currentEtag)) {
                throw new IllegalStateException("ETag mismatch");
            }
        } else {
            if ("*".equals(ifMatch)) throw new IllegalStateException("Precondition failed (no existing doc)");
        }

        Map<String,Object> p = new HashMap<>(key);
        p.put("agentJson", agentJson);
        p.put("contentType", contentType);
        p.put("content", content);
        p.put("etag", newEtag);
        mapper.upsert(p);
        return newEtag;
    }

    public Map<String,Object> getState(String activityId, String agentJson, String registration, String stateId) {
        Map<String,Object> key = new HashMap<>();
        key.put("docType", "state");
        key.put("activityId", activityId);
        key.put("agentSha", agentJson!=null?HashUtil.sha256Hex(agentJson):null);
        key.put("registration", registration);
        key.put("stateId", stateId);
        return mapper.findOne(key);
    }

    public boolean deleteState(String activityId, String agentJson, String registration, String stateId) {
        Map<String,Object> key = new HashMap<>();
        key.put("docType", "state");
        key.put("activityId", activityId);
        key.put("agentSha", agentJson!=null?HashUtil.sha256Hex(agentJson):null);
        key.put("registration", registration);
        key.put("stateId", stateId);
        return mapper.delete(key) > 0;
    }

    private String stripQuotes(String s) {
        return s == null ? null : s.replace("\"","").trim();
    }
}
