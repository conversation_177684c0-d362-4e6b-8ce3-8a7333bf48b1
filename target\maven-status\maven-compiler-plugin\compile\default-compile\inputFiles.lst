C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\mapper\StatementMapper.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\config\GlobalExceptionHandler.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\api\StatementsController.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\service\StatementService.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\service\DocumentService.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\config\BasicAuthFilter.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\LrsApplication.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\util\HashUtil.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\api\ActivitiesStateController.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\domain\Statement.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\api\LogController.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\util\JsonUtil.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\mapper\DocumentMapper.java
C:\work\gemini\internal-lrs-full\src\main\java\com\example\lrs\api\HealthController.java
